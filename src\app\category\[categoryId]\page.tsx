'use client'

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import { supabase } from '@/lib/supabase';
import { Product, Category } from '@/types/product';
import { ProductCard } from '@/components/ProductCard';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Filter, SortAsc } from 'lucide-react';

export default function CategoryDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const categoryId = params.categoryId as string;

  const [category, setCategory] = useState<Category | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoadingCategory, setIsLoadingCategory] = useState(true);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch category details
  const fetchCategory = async () => {
    try {
      setIsLoadingCategory(true);
      setError(null); // Clear any previous errors

      const { data, error } = await supabase
        .from('shop_subcategories')
        .select('*')
        .eq('id', categoryId)
        .single();

      if (error) {
        console.error('Error fetching category:', error);
        setError(`Category not found: ${error.message}`);
        return;
      }

      setCategory(data);
    } catch (error) {
      console.error('Error fetching category:', error);
      setError(`Failed to load category: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoadingCategory(false);
    }
  };

  // Fetch products for this category
  const fetchProducts = async () => {
    try {
      setIsLoadingProducts(true);
      setError(null); // Clear any previous errors

      const { data, error } = await supabase
        .from('shop_products')
        .select(`
          *,
          shop_subcategories (
            id,
            name
          )
        `)
        .eq('subcategory_id', categoryId);

      if (error) {
        console.error('Error fetching products:', error);
        setError(`Failed to load products: ${error.message}`);
        return;
      }

      setProducts(data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
      setError(`Failed to load products: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoadingProducts(false);
    }
  };

  useEffect(() => {
    if (categoryId) {
      fetchCategory();
      fetchProducts();
    }
  }, [categoryId]);

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="pt-16 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Error</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => router.back()}>
              Go Back
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="pt-16">
        {/* Category Title Section */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              {isLoadingCategory ? (
                <div className="h-8 w-48 bg-gray-200 animate-pulse rounded"></div>
              ) : (
                <h1 className="text-3xl font-bold text-gray-900">
                  {category?.name || 'Category'}
                </h1>
              )}
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Sponsored Banner */}
          <Card className="mb-8 bg-gradient-to-r from-[#f97316] to-[#ea580c] text-white border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <Badge className="bg-white/20 text-white mb-2">
                    Sponsored
                  </Badge>
                  <h2 className="text-2xl font-bold mb-2">
                    Special Offers on {category?.name || 'Construction Materials'}
                  </h2>
                  <p className="text-orange-100 mb-4">
                    Get the best deals on premium quality products. Limited time offer!
                  </p>
                  <Button className="bg-white text-[#f97316] hover:bg-gray-100">
                    View Offers
                  </Button>
                </div>
                <div className="hidden md:block w-32 h-32 bg-white/20 rounded-full flex items-center justify-center overflow-hidden ml-6">
                  <Image
                    src="/category/default.png"
                    alt="Sponsored"
                    width={96}
                    height={96}
                    className="rounded-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder-product.svg';
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Category Description */}
          {category?.description && (
            <div className="mb-6">
              <p className="text-gray-600">{category.description}</p>
            </div>
          )}

          {/* Filter and Sort Bar */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline" size="sm">
                <SortAsc className="h-4 w-4 mr-2" />
                Sort
              </Button>
            </div>
            
            <div className="text-sm text-gray-600">
              {isLoadingProducts ? (
                <div className="h-4 w-24 bg-gray-200 animate-pulse rounded"></div>
              ) : (
                `${products.length} products found`
              )}
            </div>
          </div>

          {/* Products Grid */}
          {isLoadingProducts ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <div key={index} className="bg-white rounded-lg shadow-sm border p-4">
                  <div className="w-full h-48 bg-gray-200 animate-pulse rounded-lg mb-4"></div>
                  <div className="h-4 bg-gray-200 animate-pulse rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 animate-pulse rounded w-2/3 mb-2"></div>
                  <div className="h-6 bg-gray-200 animate-pulse rounded w-1/3"></div>
                </div>
              ))}
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                <Image
                  src="/placeholder-product.svg"
                  alt="No products"
                  width={48}
                  height={48}
                  className="opacity-50"
                />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No products found
              </h3>
              <p className="text-gray-600">
                We couldn't find any products in this category. Please check back later.
              </p>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
}
