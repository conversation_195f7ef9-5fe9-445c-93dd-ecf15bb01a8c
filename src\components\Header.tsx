'use client'

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCart } from '@/contexts/CartContext';
import { 
  Search, 
  MapPin, 
  ShoppingCart, 
  Menu, 
  X,
  ChevronDown,
  User,
  Heart,
  Bell
} from 'lucide-react';

export function Header() {
  const router = useRouter();
  const { getTotalItems } = useCart();
  const [showMobileBanner, setShowMobileBanner] = useState(true);
  const [currentLocation, setCurrentLocation] = useState<string>("Select Location");
  const [isDetectingLocation, setIsDetectingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string>("");

  const handleUseAppClick = () => {
    // Handle app download/redirect logic
    console.log('Use App clicked');
  };

  const detectLocation = async () => {
    setIsDetectingLocation(true);
    setLocationError("");
    
    if (!navigator.geolocation) {
      setLocationError("Geolocation is not supported by this browser.");
      setIsDetectingLocation(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;
          
          // Use a reverse geocoding service to get the address
          const response = await fetch(
            `https://api.opencagedata.com/geocode/v1/json?q=${latitude}+${longitude}&key=YOUR_API_KEY`
          );
          
          if (response.ok) {
            const data = await response.json();
            if (data.results && data.results.length > 0) {
              const address = data.results[0].formatted;
              setCurrentLocation(address);
            } else {
              setCurrentLocation(`${latitude.toFixed(4)}, ${longitude.toFixed(4)}`);
            }
          } else {
            setCurrentLocation(`${latitude.toFixed(4)}, ${longitude.toFixed(4)}`);
          }
        } catch (error) {
          console.error('Error getting location:', error);
          setLocationError("Failed to get location details.");
        } finally {
          setIsDetectingLocation(false);
        }
      },
      (error) => {
        console.error('Geolocation error:', error);
        setLocationError("Unable to retrieve your location.");
        setIsDetectingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    );
  };

  return (
    <>
      {/* Mobile-Only App Download Banner */}
      {showMobileBanner && (
        <div className="md:hidden bg-green-600 text-white px-4 py-3 sticky top-0 z-50 shadow-sm">
          <div className="flex items-center justify-between">
            {/* Close button on the left */}
            <button
              onClick={() => setShowMobileBanner(false)}
              className="flex items-center justify-center w-6 h-6 rounded-full hover:bg-green-700 transition-colors"
              aria-label="Close banner"
            >
              <X className="h-4 w-4" />
            </button>
            
            {/* Center content */}
            <div className="flex items-center space-x-2 flex-1 justify-center">
              <Image
                src="/favicon.png"
                alt="Infratask"
                width={20}
                height={20}
                className="rounded-sm"
              />
              <span className="text-sm font-medium">Get The App for Better Experience</span>
            </div>
            
            {/* Use App button on the right */}
            <Button 
              size="sm" 
              onClick={handleUseAppClick}
              className="bg-black hover:bg-gray-800 text-white rounded-lg px-4 py-1"
            >
              Use App
            </Button>
          </div>
        </div>
      )}

      {/* Enhanced Fixed Header */}
      <header className={`bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 fixed left-0 right-0 z-40 ${showMobileBanner ? 'md:top-0 top-12' : 'top-0'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo - Hidden on mobile */}
            <div className="hidden md:flex items-center">
              <Image
                src="/logo.png"
                alt="Infratask Logo"
                width={40}
                height={40}
                className="mr-3 rounded-lg"
              />
              <div className="text-2xl font-bold text-[#f97316]">
                Infratask
              </div>
            </div>

            {/* Mobile Menu Button */}
            <Button variant="ghost" size="sm" className="md:hidden">
              <Menu className="h-5 w-5" />
            </Button>

            {/* Search Bar - Hidden on mobile */}
            <div className="hidden md:flex flex-1 max-w-2xl mx-8">
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search for construction materials, tools, equipment..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#f97316] focus:border-transparent bg-gray-50 hover:bg-white transition-colors"
                />
              </div>
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center space-x-2">
              {/* Location Selector - Hidden on mobile */}
              <div className="hidden lg:flex items-center">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={detectLocation}
                  disabled={isDetectingLocation}
                  className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl max-w-48 truncate"
                >
                  <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                  <span className="truncate">
                    {isDetectingLocation ? "Detecting..." : currentLocation}
                  </span>
                  <ChevronDown className="h-3 w-3 ml-1 flex-shrink-0" />
                </Button>
                {locationError && (
                  <div className="absolute top-full mt-1 text-xs text-red-600 bg-white p-2 rounded shadow-lg border">
                    {locationError}
                  </div>
                )}
              </div>

              {/* User Account */}
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl p-2"
              >
                <User className="h-4 w-4" />
              </Button>

              {/* Wishlist */}
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl relative p-2"
              >
                <Heart className="h-4 w-4" />
                <Badge className="absolute -top-1 -right-1 bg-[#f97316] text-white rounded-full h-5 w-5 text-xs flex items-center justify-center">
                  2
                </Badge>
              </Button>

              {/* Notifications */}
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl relative p-2"
              >
                <Bell className="h-4 w-4" />
                <Badge className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full h-5 w-5 text-xs flex items-center justify-center">
                  1
                </Badge>
              </Button>
              
              {/* Shopping Cart */}
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl relative p-2"
              >
                <ShoppingCart className="h-4 w-4" />
                <Badge className="absolute -top-1 -right-1 bg-[#f97316] text-white rounded-full h-5 w-5 text-xs flex items-center justify-center">
                  {getTotalItems()}
                </Badge>
              </Button>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}
