'use client'

import React from 'react';
import { Phone, Mail } from 'lucide-react';

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8 mt-16">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-2xl font-bold text-[#f97316] mb-4">Infratask</h3>
            <p className="text-gray-300 mb-4">Your trusted partner for construction materials and project management solutions.</p>
            <div className="flex space-x-4">
              <Phone className="h-5 w-5 text-gray-400" />
              <span className="text-gray-300">+****************</span>
            </div>
            <div className="flex space-x-4 mt-2">
              <Mail className="h-5 w-5 text-gray-400" />
              <span className="text-gray-300"><EMAIL></span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">About Us</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Services</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Products</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Contact</a></li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Categories</h4>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Cement & Concrete</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Steel & Metal</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Tools & Equipment</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Electrical</a></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Support</h4>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Help Center</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Track Order</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Returns</a></li>
              <li><a href="#" className="text-gray-300 hover:text-[#f97316] transition-colors">Privacy Policy</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-gray-700 mt-8 pt-8 text-center">
          <p className="text-gray-400">&copy; 2024 Infratask. All rights reserved. Built for construction professionals.</p>
        </div>
      </div>
    </footer>
  );
}
